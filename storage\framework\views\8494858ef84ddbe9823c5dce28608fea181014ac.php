<?php $__env->startSection('title', 'แก้ไขบริการ - ระบบจัดการ'); ?>

<?php $__env->startSection('breadcrumb'); ?>
<li class="breadcrumb-item"><a href="<?php echo e(route('admin.services')); ?>">จัดการบริการ</a></li>
<li class="breadcrumb-item active">แก้ไขบริการ</li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">แก้ไขบริการ: <?php echo e($service->title); ?></h1>
    <a href="<?php echo e(route('admin.services')); ?><?php echo e(isset($page) && $page > 1 ? '?page=' . $page : ''); ?>" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>กลับ
    </a>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-body">
                <form action="<?php echo e(route('admin.services.update', $service->id)); ?>" method="POST" enctype="multipart/form-data">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('PUT'); ?>
                    <input type="hidden" name="page" value="<?php echo e($page ?? 1); ?>">
                    
                    <div class="mb-3">
                        <label for="title" class="form-label">ชื่อบริการ <span class="text-danger">*</span></label>
                        <input type="text" class="form-control <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                               id="title" name="title" value="<?php echo e(old('title', $service->title)); ?>" required>
                        <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">คำอธิบายสั้น <span class="text-danger">*</span></label>
                        <textarea class="form-control <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                  id="description" name="description" rows="3" required><?php echo e(old('description', $service->description)); ?></textarea>
                        <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    
                    <div class="mb-3">
                        <label for="details" class="form-label">รายละเอียด</label>
                        <textarea class="form-control <?php $__errorArgs = ['details'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                  id="details" name="details" rows="5"><?php echo e(old('details', $service->details)); ?></textarea>
                        <?php $__errorArgs = ['details'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    
                    <div class="mb-3">
                        <label for="sort_order" class="form-label">ลำดับการแสดง</label>
                        <input type="number" class="form-control <?php $__errorArgs = ['sort_order'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                               id="sort_order" name="sort_order" value="<?php echo e(old('sort_order', $service->sort_order)); ?>" min="0">
                        <?php $__errorArgs = ['sort_order'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    
                    <div class="mb-3">
                        <label for="image" class="form-label">รูปภาพหลัก</label>
                        <?php if($service->image): ?>
                        <div class="mb-2">
                            <img src="<?php echo e(asset('storage/' . $service->image)); ?>" alt="<?php echo e($service->title); ?>"
                                 class="img-thumbnail" style="max-width: 200px;">
                            <div class="form-text">รูปภาพปัจจุบัน</div>
                        </div>
                        <?php endif; ?>
                        <input type="file" class="form-control <?php $__errorArgs = ['image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                               id="image" name="image" accept="image/*">
                        <?php $__errorArgs = ['image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        <div class="form-text">รองรับไฟล์: JPG, PNG, GIF ขนาดไม่เกิน 2MB (เลือกไฟล์ใหม่หากต้องการเปลี่ยน)</div>
                    </div>

                    <!-- Existing Gallery Images -->
                    <?php if($service->images->count() > 0): ?>
                    <div class="mb-4">
                        <label class="form-label">รูปภาพแกลอรี่ที่มีอยู่</label>
                        <div id="existing-gallery" class="row g-3">
                            <?php $__currentLoopData = $service->images->sortBy('sort_order'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="col-md-6 col-lg-4" data-image-id="<?php echo e($image->id); ?>">
                                <div class="card">
                                    <div class="position-relative">
                                        <img src="<?php echo e(asset('storage/' . $image->image_path)); ?>"
                                             class="card-img-top" style="height: 200px; object-fit: cover;">
                                        <?php if($image->is_cover): ?>
                                        <span class="position-absolute top-0 start-0 badge bg-primary m-2">รูปปก</span>
                                        <?php endif; ?>
                                        <div class="position-absolute top-0 end-0 m-2">
                                            <div class="btn-group-vertical">
                                                <?php if(!$image->is_cover): ?>
                                                <button type="button" class="btn btn-sm btn-success set-cover-btn"
                                                        data-image-id="<?php echo e($image->id); ?>" title="ตั้งเป็นรูปปก">
                                                    <i class="fas fa-star"></i>
                                                </button>
                                                <?php endif; ?>
                                                <button type="button" class="btn btn-sm btn-danger delete-image-btn"
                                                        data-image-id="<?php echo e($image->id); ?>" title="ลบรูป">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-body p-2">
                                        <div class="mb-2">
                                            <input type="text" class="form-control form-control-sm image-alt-text"
                                                   value="<?php echo e($image->alt_text); ?>" placeholder="Alt Text"
                                                   data-image-id="<?php echo e($image->id); ?>">
                                        </div>
                                        <div class="mb-2">
                                            <input type="text" class="form-control form-control-sm image-description"
                                                   value="<?php echo e($image->description); ?>" placeholder="คำอธิบาย"
                                                   data-image-id="<?php echo e($image->id); ?>">
                                        </div>
                                        <button type="button" class="btn btn-sm btn-outline-primary update-image-btn w-100"
                                                data-image-id="<?php echo e($image->id); ?>">
                                            <i class="fas fa-save me-1"></i>บันทึก
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Add New Gallery Images -->
                    <div class="mb-3">
                        <label class="form-label">เพิ่มรูปภาพแกลอรี่ใหม่</label>
                        <div id="new-gallery-container">
                            <div class="new-gallery-item mb-3 p-3 border rounded">
                                <div class="row">
                                    <div class="col-md-4">
                                        <label class="form-label">รูปภาพ</label>
                                        <input type="file" class="form-control new-gallery-image"
                                               name="new_gallery_images[]" accept="image/*">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Alt Text</label>
                                        <input type="text" class="form-control"
                                               name="new_gallery_alt_texts[]" placeholder="คำอธิบายรูปภาพ">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">คำอธิบาย</label>
                                        <div class="input-group">
                                            <input type="text" class="form-control"
                                                   name="new_gallery_descriptions[]" placeholder="คำอธิบายเพิ่มเติม">
                                            <button type="button" class="btn btn-outline-danger remove-new-gallery-item">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button type="button" class="btn btn-outline-primary" id="add-new-gallery-item">
                            <i class="fas fa-plus me-2"></i>เพิ่มรูปภาพใหม่
                        </button>
                        <div class="form-text">รองรับไฟล์: JPG, PNG, GIF ขนาดไม่เกิน 2MB</div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                   value="1" <?php echo e(old('is_active', $service->is_active) ? 'checked' : ''); ?>>
                            <label class="form-check-label" for="is_active">
                                เปิดใช้งาน
                            </label>
                        </div>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>บันทึกการแก้ไข
                        </button>
                        <a href="<?php echo e(route('admin.services')); ?><?php echo e(isset($page) && $page > 1 ? '?page=' . $page : ''); ?>" class="btn btn-secondary">ยกเลิก</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">ข้อมูลบริการ</h5>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>สร้างเมื่อ:</strong></td>
                        <td><?php echo e($service->created_at->format('d/m/Y H:i')); ?></td>
                    </tr>
                    <tr>
                        <td><strong>แก้ไขล่าสุด:</strong></td>
                        <td><?php echo e($service->updated_at->format('d/m/Y H:i')); ?></td>
                    </tr>
                    <tr>
                        <td><strong>สถานะ:</strong></td>
                        <td>
                            <?php if($service->is_active): ?>
                            <span class="badge bg-success">เปิดใช้งาน</span>
                            <?php else: ?>
                            <span class="badge bg-secondary">ปิดใช้งาน</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">การดำเนินการ</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="<?php echo e(route('services')); ?>" class="btn btn-outline-primary" target="_blank">
                        <i class="fas fa-eye me-2"></i>ดูในเว็บไซต์
                    </a>
                    <form action="<?php echo e(route('admin.services.delete', $service->id)); ?>" method="POST"
                          id="deleteServiceForm<?php echo e($service->id); ?>">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="button" class="btn btn-outline-danger w-100"
                                onclick="handleDeleteService(<?php echo e($service->id); ?>)">
                            <i class="fas fa-trash me-2"></i>ลบบริการ
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startSection('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const newGalleryContainer = document.getElementById('new-gallery-container');
    const addNewGalleryBtn = document.getElementById('add-new-gallery-item');

    // Add new gallery item
    addNewGalleryBtn.addEventListener('click', function() {
        const galleryItem = document.createElement('div');
        galleryItem.className = 'new-gallery-item mb-3 p-3 border rounded';
        galleryItem.innerHTML = `
            <div class="row">
                <div class="col-md-4">
                    <label class="form-label">รูปภาพ</label>
                    <input type="file" class="form-control new-gallery-image"
                           name="new_gallery_images[]" accept="image/*">
                </div>
                <div class="col-md-4">
                    <label class="form-label">Alt Text</label>
                    <input type="text" class="form-control"
                           name="new_gallery_alt_texts[]" placeholder="คำอธิบายรูปภาพ">
                </div>
                <div class="col-md-4">
                    <label class="form-label">คำอธิบาย</label>
                    <div class="input-group">
                        <input type="text" class="form-control"
                               name="new_gallery_descriptions[]" placeholder="คำอธิบายเพิ่มเติม">
                        <button type="button" class="btn btn-outline-danger remove-new-gallery-item">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
        newGalleryContainer.appendChild(galleryItem);
    });

    // Remove new gallery item
    newGalleryContainer.addEventListener('click', function(e) {
        if (e.target.closest('.remove-new-gallery-item')) {
            const galleryItem = e.target.closest('.new-gallery-item');
            if (newGalleryContainer.children.length > 1) {
                galleryItem.remove();
            } else {
                // Clear the inputs instead of removing the last item
                const inputs = galleryItem.querySelectorAll('input');
                inputs.forEach(input => input.value = '');
            }
        }
    });

    // Delete existing image
    document.addEventListener('click', function(e) {
        if (e.target.closest('.delete-image-btn')) {
            const imageId = e.target.closest('.delete-image-btn').dataset.imageId;

            if (confirm('คุณแน่ใจหรือไม่ที่จะลบรูปนี้?')) {
                fetch(`/admin/services/images/${imageId}`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        'Content-Type': 'application/json',
                    },
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        e.target.closest('.col-md-6, .col-lg-4').remove();
                        alert(data.message);
                    } else {
                        alert(data.message || 'เกิดข้อผิดพลาด');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('เกิดข้อผิดพลาดในการลบรูป');
                });
            }
        }
    });

    // Set cover image
    document.addEventListener('click', function(e) {
        if (e.target.closest('.set-cover-btn')) {
            const imageId = e.target.closest('.set-cover-btn').dataset.imageId;

            fetch(`/admin/services/images/${imageId}/set-cover`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Remove all cover badges
                    document.querySelectorAll('.badge.bg-primary').forEach(badge => badge.remove());
                    document.querySelectorAll('.set-cover-btn').forEach(btn => btn.style.display = 'block');

                    // Add cover badge to this image
                    const imageCard = e.target.closest('.col-md-6, .col-lg-4');
                    const imgContainer = imageCard.querySelector('.position-relative');
                    const coverBadge = document.createElement('span');
                    coverBadge.className = 'position-absolute top-0 start-0 badge bg-primary m-2';
                    coverBadge.textContent = 'รูปปก';
                    imgContainer.appendChild(coverBadge);

                    // Hide this button
                    e.target.closest('.set-cover-btn').style.display = 'none';

                    alert(data.message);
                } else {
                    alert(data.message || 'เกิดข้อผิดพลาด');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('เกิดข้อผิดพลาดในการตั้งรูปปก');
            });
        }
    });

    // Update image info
    document.addEventListener('click', function(e) {
        if (e.target.closest('.update-image-btn')) {
            const imageId = e.target.closest('.update-image-btn').dataset.imageId;
            const card = e.target.closest('.card');
            const altText = card.querySelector('.image-alt-text').value;
            const description = card.querySelector('.image-description').value;

            fetch(`/admin/services/images/${imageId}`, {
                method: 'PUT',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    alt_text: altText,
                    description: description
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                } else {
                    alert(data.message || 'เกิดข้อผิดพลาด');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('เกิดข้อผิดพลาดในการอัปเดตข้อมูล');
            });
        }
    });
});

// Delete service function with custom modal
async function handleDeleteService(serviceId) {
    const confirmed = await confirmDelete(
        'คุณแน่ใจหรือไม่ที่จะลบบริการนี้? การกระทำนี้ไม่สามารถยกเลิกได้',
        'ยืนยันการลบบริการ'
    );

    if (confirmed) {
        document.getElementById(`deleteServiceForm${serviceId}`).submit();
    }
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\XAMPP\htdocs\PhuyaiPrajakserviceshop\resources\views/admin/services/edit.blade.php ENDPATH**/ ?>