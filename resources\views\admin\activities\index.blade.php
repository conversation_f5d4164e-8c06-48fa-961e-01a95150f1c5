@extends('layouts.admin')

@section('title', 'จัดการผลงาน - ระบบจัดการ')

@section('breadcrumb')
<li class="breadcrumb-item active">จัดการผลงาน</li>
@endsection

@section('content')
<!-- Header Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-1" style="background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">
                    <i class="fas fa-images me-2"></i>ผลงานการให้บริการ
                </h1>
                <p class="text-muted mb-0">จัดการผลงานและแกลเลอรี่รูปภาพ</p>
            </div>
            <div class="d-flex gap-2">
                <button class="btn btn-outline-secondary" id="toggleViewBtn">
                    <i class="fas fa-th-large me-2" id="viewIcon"></i>
                    <span id="viewText">มุมมองการ์ด</span>
                </button>
                <a href="{{ route('admin.activities.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>เพิ่มผลงานใหม่
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Stats Cards -->
<div class="row g-4 mb-4">
    <div class="col-md-4">
        <div class="card stats-card h-100 interactive-card" data-aos="fade-up" data-aos-delay="100">
            <div class="card-body text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50 mb-1">ผลงานทั้งหมด</h6>
                        <h3 class="mb-0 counter" data-target="{{ $totalActivities }}">0</h3>
                    </div>
                    <i class="fas fa-images fa-2x text-white-50"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card h-100 interactive-card" style="background: linear-gradient(135deg, var(--success-color), #059669);" data-aos="fade-up" data-aos-delay="200">
            <div class="card-body text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50 mb-1">รูปที่เผยแพร่</h6>
                        <h3 class="mb-0 counter" data-target="{{ $activeActivities }}">0</h3>
                    </div>
                    <i class="fas fa-check-circle fa-2x text-white-50"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card h-100 interactive-card" style="background: linear-gradient(135deg, var(--info-color), #0891b2);" data-aos="fade-up" data-aos-delay="300">
            <div class="card-body text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50 mb-1">รูปเดือนนี้</h6>
                        <h3 class="mb-0 counter" data-target="{{ $thisMonthActivities }}">0</h3>
                    </div>
                    <i class="fas fa-calendar fa-2x text-white-50"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Activities Content -->
<div class="card interactive-card" data-aos="fade-up" data-aos-delay="400">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="fas fa-images me-2"></i>แกลลอรี่รูปภาพ
            </h5>
            <div class="d-flex gap-2 align-items-center">
                <div class="input-group" style="width: 300px;">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" class="form-control" placeholder="ค้นหารูปภาพ..." id="searchInput" onkeyup="filterActivities()">
                </div>

                @if($activities->hasPages())
                <div class="text-muted small">
                    <i class="fas fa-layer-group me-1"></i>
                    หน้า {{ $activities->currentPage() }} / {{ $activities->lastPage() }}
                </div>
                @endif
            </div>
        </div>
    </div>
    <div class="card-body">
        @if($activities->count() > 0)

        <!-- Loading Skeleton (Hidden by default) -->
        <div id="loadingSkeleton" style="display: none;">
            <div class="row g-3">
                @for($i = 0; $i < 6; $i++)
                <div class="col-md-4">
                    <div class="card">
                        <div class="skeleton skeleton-image"></div>
                        <div class="card-body">
                            <div class="skeleton skeleton-title"></div>
                            <div class="skeleton skeleton-text"></div>
                            <div class="skeleton skeleton-text" style="width: 60%;"></div>
                        </div>
                    </div>
                </div>
                @endfor
            </div>
        </div>

        <!-- Table View -->
        <div id="tableView">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th style="width: 120px;">รูปปก</th>
                            <th>ชื่อเรื่อง</th>
                            <th>คำอธิบาย</th>
                            <th style="width: 100px;">รูปภาพ</th>
                            <th style="width: 120px;">วันที่</th>
                            <th style="width: 100px;">สถานะ</th>
                            <th style="width: 150px;">การจัดการ</th>
                        </tr>
                    </thead>
                    <tbody id="activitiesTableBody">
                        @foreach($activities as $index => $activity)
                        <tr class="activity-row" data-aos="fade-up" data-aos-delay="{{ 100 + ($index * 50) }}">
                            <td>
                                @php
                                    $coverImage = $activity->images->where('is_cover', true)->first() ?? $activity->images->first();
                                    $coverImagePath = $coverImage ? $coverImage->image_path : $activity->image;
                                @endphp
                                @if($coverImagePath)
                                <img src="{{ asset('storage/' . $coverImagePath) }}" alt="{{ $activity->title }}"
                                     class="img-thumbnail rounded" style="width: 80px; height: 80px; object-fit: cover; cursor: pointer;"
                                     onclick="viewImage('{{ asset('storage/' . $coverImagePath) }}', '{{ $activity->title }}')">
                                @else
                                <div class="bg-secondary bg-opacity-10 d-flex align-items-center justify-content-center rounded"
                                     style="width: 80px; height: 80px;">
                                    <i class="fas fa-images text-muted"></i>
                                </div>
                                @endif
                            </td>
                            <td>
                                <div>
                                    <strong class="activity-title">{{ $activity->title }}</strong>
                                    <br>
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>
                                        {{ $activity->activity_date->format('d/m/Y') }}
                                        @if($activity->location)
                                        <br><i class="fas fa-map-marker-alt me-1"></i>{{ Str::limit($activity->location, 30) }}
                                        @endif
                                    </small>
                                </div>
                            </td>
                            <td>
                                <span class="activity-description">{{ Str::limit($activity->description, 60) }}</span>
                            </td>
                            <td>
                                <div class="text-center">
                                    <span class="badge bg-primary">
                                        <i class="fas fa-images me-1"></i>
                                        {{ $activity->images->count() }} รูป
                                    </span>
                                    @if($activity->images->count() > 0)
                                    <br><small class="text-muted">รูปปก: {{ $activity->images->where('is_cover', true)->count() > 0 ? 'มี' : 'ไม่มี' }}</small>
                                    @endif
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-info">{{ $activity->activity_date->format('d/m/Y') }}</span>
                            </td>
                            <td>
                                @if($activity->is_active)
                                <span class="badge bg-success">
                                    <i class="fas fa-check me-1"></i>เปิดใช้
                                </span>
                                @else
                                <span class="badge bg-secondary">
                                    <i class="fas fa-times me-1"></i>ปิดใช้
                                </span>
                                @endif
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('activities.show', $activity->id) }}" target="_blank"
                                       class="btn btn-sm btn-outline-success" title="ดูหน้าเว็บ">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.activities.edit', $activity->id) }}{{ request()->has('page') ? '?page=' . request('page') : '' }}"
                                       class="btn btn-sm btn-outline-primary" title="แก้ไข">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="{{ route('admin.activities.delete', $activity->id) }}"
                                          method="POST" class="d-inline" id="deleteActivityTableForm{{ $activity->id }}">
                                        @csrf
                                        @method('DELETE')
                                        <button type="button" class="btn btn-sm btn-outline-danger"
                                                onclick="handleDeleteActivityTable({{ $activity->id }})" title="ลบ">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Gallery View -->
        <div id="cardView" style="display: none;">
            <div class="row g-3">
                @foreach($activities as $activity)
                <div class="col-md-3 col-sm-4 col-6 activity-card">
                    <div class="card h-100 interactive-card gallery-item">
                        @php
                            $coverImage = $activity->images->where('is_cover', true)->first() ?? $activity->images->first();
                            $coverImagePath = $coverImage ? $coverImage->image_path : $activity->image;
                        @endphp
                        @if($coverImagePath)
                        <div class="card-image-container img-size-large">
                            <img src="{{ asset('storage/' . $coverImagePath) }}"
                                 class="img-fit-contain"
                                 style="cursor: pointer; height: 200px; width: 100%; object-fit: cover;"
                                 alt="{{ $activity->title }}"
                                 data-bs-toggle="modal" data-bs-target="#imageModal"
                                 data-image="{{ asset('storage/' . $coverImagePath) }}"
                                 data-title="{{ $activity->title }}"
                                 data-description="{{ $activity->description }}">

                            <!-- Overlay with status -->
                            <div class="position-absolute top-0 end-0 m-2">
                                @if($activity->is_active)
                                <span class="badge bg-success bg-opacity-90">
                                    <i class="fas fa-eye me-1"></i>เผยแพร่
                                </span>
                                @else
                                <span class="badge bg-secondary bg-opacity-90">
                                    <i class="fas fa-eye-slash me-1"></i>ซ่อน
                                </span>
                                @endif
                            </div>

                            <!-- Image count badge -->
                            @if($activity->images->count() > 1)
                            <div class="position-absolute top-0 start-0 m-2">
                                <span class="badge bg-primary bg-opacity-90">
                                    <i class="fas fa-images me-1"></i>{{ $activity->images->count() }}
                                </span>
                            </div>
                            @endif

                            <!-- Hover overlay -->
                            <div class="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center bg-dark bg-opacity-50 opacity-0 hover-overlay" style="transition: opacity 0.3s ease;">
                                <i class="fas fa-search-plus fa-2x text-white"></i>
                            </div>
                        </div>
                        @else
                        <div class="card-img-top bg-secondary bg-opacity-10 d-flex align-items-center justify-content-center"
                             style="height: 200px;">
                            <i class="fas fa-images fa-3x text-muted"></i>
                        </div>
                        @endif

                        <div class="card-body p-3">
                            <h6 class="card-title activity-title mb-2">{{ Str::limit($activity->title, 30) }}</h6>
                            <p class="card-text activity-description small text-muted mb-2">{{ Str::limit($activity->description, 60) }}</p>

                            @if($activity->location)
                            <p class="card-text activity-location small text-muted mb-2">
                                <i class="fas fa-map-marker-alt me-1"></i>{{ Str::limit($activity->location, 25) }}
                            </p>
                            @endif

                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    <i class="fas fa-calendar me-1"></i>{{ $activity->activity_date->format('d/m/Y') }}
                                </small>
                                <small class="text-muted">#{{ $activity->sort_order ?? 0 }}</small>
                            </div>
                        </div>

                        <div class="card-footer bg-transparent p-2">
                            <div class="d-flex gap-1">
                                <a href="{{ route('admin.activities.edit', $activity->id) }}{{ request()->has('page') ? '?page=' . request('page') : '' }}"
                                   class="btn btn-sm btn-primary flex-fill">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form action="{{ route('admin.activities.delete', $activity->id) }}"
                                      method="POST" class="flex-fill" id="deleteActivityCardForm{{ $activity->id }}">
                                    @csrf
                                    @method('DELETE')
                                    <button type="button" class="btn btn-sm btn-danger w-100"
                                            onclick="handleDeleteActivityCard({{ $activity->id }})">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>

        <!-- Pagination -->
        @if($activities->hasPages())
        <div class="mt-4 p-3 bg-light rounded">
            @include('custom.simple-pagination', ['paginator' => $activities])
        </div>
        @endif

        @else
        <div class="text-center py-5">
            <i class="fas fa-images fa-5x text-muted mb-4"></i>
            <h4 class="text-muted mb-2">ยังไม่มีรูปภาพ</h4>
            <p class="text-muted mb-4">เริ่มต้นด้วยการเพิ่มรูปภาพแรกของคุณ</p>
            <a href="{{ route('admin.activities.create') }}" class="btn btn-primary btn-lg">
                <i class="fas fa-plus me-2"></i>เพิ่มรูปภาพใหม่
            </a>
        </div>
        @endif
    </div>
</div>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageModalLabel">รูปภาพ</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" alt="" class="img-fluid rounded mb-3" style="max-height: 500px;">
                <h6 id="modalTitle" class="mb-2"></h6>
                <p id="modalDescription" class="text-muted"></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ปิด</button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
let currentView = 'table';

function toggleView() {
    console.log('toggleView called, currentView:', currentView);

    const tableView = document.getElementById('tableView');
    const cardView = document.getElementById('cardView');
    const viewIcon = document.getElementById('viewIcon');
    const viewText = document.getElementById('viewText');

    // Debug: Check if elements exist
    console.log('Elements found:', {
        tableView: !!tableView,
        cardView: !!cardView,
        viewIcon: !!viewIcon,
        viewText: !!viewText
    });

    if (!tableView || !cardView || !viewIcon || !viewText) {
        console.error('Required elements not found!');
        return;
    }

    if (currentView === 'table') {
        // Switch to card view
        console.log('Switching to card view');
        tableView.style.display = 'none';
        cardView.style.display = 'block';
        viewIcon.className = 'fas fa-list me-2';
        viewText.textContent = 'มุมมองตาราง';
        currentView = 'card';

        // Trigger AOS refresh for cards
        setTimeout(() => {
            if (typeof AOS !== 'undefined') {
                AOS.refresh();
            }
        }, 100);

    } else {
        // Switch to table view
        console.log('Switching to table view');
        cardView.style.display = 'none';
        tableView.style.display = 'block';
        viewIcon.className = 'fas fa-th-large me-2';
        viewText.textContent = 'มุมมองการ์ด';
        currentView = 'table';
    }

    console.log('View switched to:', currentView);
}

// Enhanced Search functionality with smooth animations
let searchTimeout;
function filterActivities() {
    clearTimeout(searchTimeout);

    searchTimeout = setTimeout(() => {
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();
        const activityRows = document.querySelectorAll('.activity-row');
        const activityCards = document.querySelectorAll('.activity-card');
        let visibleCount = 0;

        // Show loading state
        showSearchLoading();

        // Search in table view
        activityRows.forEach((row, index) => {
            const title = row.querySelector('.activity-title')?.textContent.toLowerCase() || '';
            const description = row.querySelector('.activity-description')?.textContent.toLowerCase() || '';

            if (title.includes(searchTerm) || description.includes(searchTerm)) {
                row.style.display = '';
                row.style.opacity = '0';
                row.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    row.style.transition = 'all 0.3s ease';
                    row.style.opacity = '1';
                    row.style.transform = 'translateY(0)';
                }, index * 50);

                visibleCount++;
            } else {
                row.style.transition = 'all 0.2s ease';
                row.style.opacity = '0';
                row.style.transform = 'translateY(-10px)';

                setTimeout(() => {
                    row.style.display = 'none';
                }, 200);
            }
        });

        // Search in card view
        activityCards.forEach((card, index) => {
            const title = card.querySelector('.activity-title')?.textContent.toLowerCase() || '';
            const description = card.querySelector('.activity-description')?.textContent.toLowerCase() || '';

            if (title.includes(searchTerm) || description.includes(searchTerm)) {
                card.style.display = '';
                card.style.opacity = '0';
                card.style.transform = 'scale(0.8)';

                setTimeout(() => {
                    card.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
                    card.style.opacity = '1';
                    card.style.transform = 'scale(1)';
                }, index * 100);

                visibleCount++;
            } else {
                card.style.transition = 'all 0.2s ease';
                card.style.opacity = '0';
                card.style.transform = 'scale(0.8)';

                setTimeout(() => {
                    card.style.display = 'none';
                }, 200);
            }
        });

        // Show no results message
        setTimeout(() => {
            handleNoResults(visibleCount, searchTerm);
            hideSearchLoading();
        }, 300);

    }, 300); // Debounce search
}

function showSearchLoading() {
    const searchIcon = document.querySelector('#searchInput').previousElementSibling.querySelector('i');
    searchIcon.className = 'fas fa-spinner fa-spin';
}

function hideSearchLoading() {
    const searchIcon = document.querySelector('#searchInput').previousElementSibling.querySelector('i');
    searchIcon.className = 'fas fa-search';
}

function handleNoResults(visibleCount, searchTerm) {
    const existingMsg = document.getElementById('noResultsMessage');
    if (existingMsg) existingMsg.remove();

    if (visibleCount === 0 && searchTerm.length > 0) {
        const container = currentView === 'table'
            ? document.getElementById('activitiesTableBody')
            : document.getElementById('cardView');

        const noResultsElement = document.createElement(currentView === 'table' ? 'tr' : 'div');
        noResultsElement.id = 'noResultsMessage';
        noResultsElement.className = currentView === 'table' ? '' : 'col-12 text-center py-5';

        noResultsElement.innerHTML = currentView === 'table'
            ? `<td colspan="7" class="text-center py-4">
                <div class="fade-in-up">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">ไม่พบผลงานที่ตรงกับคำค้นหา</h5>
                    <p class="text-muted">"${searchTerm}"</p>
                </div>
               </td>`
            : `<div class="fade-in-up">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">ไม่พบผลงานที่ตรงกับคำค้นหา</h5>
                <p class="text-muted">"${searchTerm}"</p>
               </div>`;

        container.appendChild(noResultsElement);
    }
}

// Bind search event
document.getElementById('searchInput').addEventListener('input', filterActivities);

// Enhanced Card Interactions
function initCardInteractions() {
    const cards = document.querySelectorAll('.activity-card .card');

    cards.forEach(card => {
        // Add ripple effect on click
        card.addEventListener('click', function(e) {
            if (e.target.closest('.btn') || e.target.closest('[data-bs-toggle="modal"]')) {
                return; // Don't add ripple to buttons or modal triggers
            }

            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.style.position = 'absolute';
            ripple.style.borderRadius = '50%';
            ripple.style.background = 'rgba(59, 130, 246, 0.3)';
            ripple.style.transform = 'scale(0)';
            ripple.style.animation = 'ripple 0.6s linear';
            ripple.style.pointerEvents = 'none';

            this.style.position = 'relative';
            this.style.overflow = 'hidden';
            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });

        // Enhanced hover effects
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px) scale(1.02)';
            this.style.boxShadow = '0 12px 30px rgba(0, 0, 0, 0.15)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = '';
        });
    });
}

// Add CSS for ripple animation
const style = document.createElement('style');
style.textContent = `
    @keyframes ripple {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);

// Initialize everything when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing...');

    // Test if elements exist
    const tableView = document.getElementById('tableView');
    const cardView = document.getElementById('cardView');
    console.log('Initial check - tableView:', !!tableView, 'cardView:', !!cardView);

    // Add animation to cards
    const cards = document.querySelectorAll('.interactive-card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('fade-in-up');
    });

    // Initialize card interactions
    initCardInteractions();

    // Bind toggle view button
    const toggleBtn = document.getElementById('toggleViewBtn');
    if (toggleBtn) {
        toggleBtn.addEventListener('click', function() {
            console.log('Toggle button clicked!');
            toggleView();
        });
        console.log('Toggle button event listener added');
    } else {
        console.error('Toggle button not found!');
    }

    // Make toggleView available globally
    window.toggleView = toggleView;

    // Simple image modal functionality
    console.log('Setting up image modal...');

    // Gallery hover effects
    const galleryItems = document.querySelectorAll('.gallery-item');
    galleryItems.forEach(item => {
        const overlay = item.querySelector('.hover-overlay');
        if (overlay) {
            item.addEventListener('mouseenter', function() {
                overlay.style.opacity = '1';
            });

            item.addEventListener('mouseleave', function() {
                overlay.style.opacity = '0';
            });
        }
    });
});

// Simple view image function
function viewImage(src, title, description = '') {
    console.log('viewImage called:', src, title);

    // Get modal elements
    const modalElement = document.getElementById('imageModal');
    const modalImage = document.getElementById('modalImage');
    const modalTitle = document.getElementById('modalTitle');
    const modalDescription = document.getElementById('modalDescription');

    if (!modalElement || !modalImage) {
        console.error('Modal elements not found');
        return;
    }

    // Set image and text
    modalImage.src = src;
    modalImage.alt = title || '';
    if (modalTitle) modalTitle.textContent = title || '';
    if (modalDescription) modalDescription.textContent = description || '';

    // Show modal using Bootstrap
    const modal = new bootstrap.Modal(modalElement);
    modal.show();
}
</script>

<!-- Temporary Debug Script -->
<script src="{{ asset('js/view-toggle-test.js') }}"></script>

<script>
// Additional debug for button click
document.addEventListener('DOMContentLoaded', function() {
    const btn = document.getElementById('toggleViewBtn');
    if (btn) {
        btn.addEventListener('click', function() {
            console.log('🔥 Button clicked - this should work now!');
        });
    }
});

// Delete activity function with custom modal (for table view)
async function handleDeleteActivityTable(activityId) {
    const confirmed = await confirmDelete(
        'คุณแน่ใจหรือไม่ที่จะลบผลงานนี้? การกระทำนี้ไม่สามารถยกเลิกได้',
        'ยืนยันการลบผลงาน'
    );

    if (confirmed) {
        document.getElementById(`deleteActivityTableForm${activityId}`).submit();
    }
}

// Delete activity function with custom modal (for card view)
async function handleDeleteActivityCard(activityId) {
    const confirmed = await confirmDelete(
        'คุณแน่ใจหรือไม่ที่จะลบผลงานนี้? การกระทำนี้ไม่สามารถยกเลิกได้',
        'ยืนยันการลบผลงาน'
    );

    if (confirmed) {
        document.getElementById(`deleteActivityCardForm${activityId}`).submit();
    }
}
</script>
@endsection
