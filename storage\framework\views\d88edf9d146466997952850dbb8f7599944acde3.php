<?php $__env->startSection('title', 'แก้ไขผลงาน - ผลงานการให้บริการ'); ?>

<?php $__env->startSection('breadcrumb'); ?>
<li class="breadcrumb-item"><a href="<?php echo e(route('admin.activities')); ?>">ผลงานการให้บริการ</a></li>
<li class="breadcrumb-item active">แก้ไขผลงาน</li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<!-- Header Section - เรียบง่าย -->
<div class="row mb-4">
    <div class="col-12">
        <div class="bg-white rounded-3 p-4 border">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-1 text-primary">
                        <i class="fas fa-edit me-2"></i>แก้ไขผลงาน
                    </h1>
                    <p class="text-muted mb-0">แก้ไขข้อมูลผลงาน: <?php echo e($activity->title); ?></p>
                </div>
                <div class="d-flex gap-2">
                    <a href="<?php echo e(route('activities.show', $activity->id)); ?>" class="btn btn-outline-primary" target="_blank">
                        <i class="fas fa-eye me-2"></i>ดูหน้าเว็บ
                    </a>
                    <a href="<?php echo e(route('admin.activities')); ?><?php echo e(isset($page) && $page > 1 ? '?page=' . $page : ''); ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>กลับไปผลงาน
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-body">
                <form action="<?php echo e(route('admin.activities.update', $activity->id)); ?>" method="POST" enctype="multipart/form-data">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('PUT'); ?>
                    <input type="hidden" name="page" value="<?php echo e($page ?? 1); ?>">
                    
                    <!-- Current Images Gallery -->
                    <?php if($activity->images->count() > 0): ?>
                    <div class="mb-4">
                        <label class="form-label">แกลเลอรี่รูปภาพปัจจุบัน</label>
                        <div class="row g-2" id="currentGallery">
                            <?php $__currentLoopData = $activity->images->sortBy('sort_order'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="col-md-3 col-4" id="image-<?php echo e($image->id); ?>">
                                <div class="card position-relative">
                                    <img src="<?php echo e(asset('storage/' . $image->image_path)); ?>"
                                         class="card-img-top"
                                         alt="<?php echo e($image->caption); ?>"
                                         style="height: 120px; object-fit: cover; cursor: pointer;"
                                         onclick="viewImage('<?php echo e(asset('storage/' . $image->image_path)); ?>', '<?php echo e($image->caption); ?>')">

                                    <?php if($image->is_cover): ?>
                                    <div class="position-absolute top-0 end-0 m-1">
                                        <span class="badge bg-primary">รูปปก</span>
                                    </div>
                                    <?php endif; ?>

                                    <div class="card-body p-2">
                                        <small class="text-muted d-block"><?php echo e(Str::limit($image->caption, 30)); ?></small>
                                        <div class="d-flex gap-1 mt-1">
                                            <?php if(!$image->is_cover): ?>
                                            <button type="button" class="btn btn-outline-primary btn-sm"
                                                    onclick="setCover(<?php echo e($image->id); ?>)" title="ตั้งเป็นรูปปก">
                                                <i class="fas fa-star"></i>
                                            </button>
                                            <?php endif; ?>
                                            <button type="button" class="btn btn-outline-danger btn-sm"
                                                    onclick="deleteImage(<?php echo e($image->id); ?>)" title="ลบรูป">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                    <?php else: ?>
                    <div class="mb-4">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            ยังไม่มีรูปภาพในแกลเลอรี่ กรุณาเพิ่มรูปภาพด้านล่าง
                            </div>
                    <?php endif; ?>

                    <!-- Change Cover Image -->
                    <div class="mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-star me-2"></i>เปลี่ยนรูปปก
                                </h6>
                            </div>
                            <div class="card-body">
                                <label for="cover_image" class="form-label">เลือกรูปปกใหม่</label>
                                <input type="file" class="form-control <?php $__errorArgs = ['cover_image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       id="cover_image" name="cover_image" accept="image/*">
                                <?php $__errorArgs = ['cover_image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    เปลี่ยนรูปปกของผลงานนี้ รองรับไฟล์: JPG, PNG, GIF ขนาดไม่เกิน 2MB
                                </div>

                                <!-- Cover Image Preview -->
                                <div id="coverImagePreview" class="mt-3" style="display: none;">
                                    <div class="card">
                                        <img id="coverPreviewImg" src="" alt="Preview" class="card-img-top" style="height: 200px; object-fit: cover;">
                                        <div class="card-body p-2">
                                            <small class="text-muted">รูปปกใหม่</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Add New Images to Gallery -->
                    <div class="mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-plus me-2"></i>เพิ่มรูปใหม่ในแกลเลอรี่
                                </h6>
                            </div>
                            <div class="card-body">
                                <label for="new_images" class="form-label">เลือกรูปภาพใหม่</label>
                                <input type="file" class="form-control <?php $__errorArgs = ['new_images'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?> <?php $__errorArgs = ['new_images.*'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       id="new_images" name="new_images[]" accept="image/*" multiple>
                                <?php $__errorArgs = ['new_images'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                <?php $__errorArgs = ['new_images.*'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    เพิ่มรูปเข้าไปในแกลเลอรี่ สามารถเลือกหลายรูปพร้อมกัน รองรับไฟล์: JPG, PNG, GIF ขนาดไม่เกิน 2MB ต่อรูป
                                </div>

                                <!-- New Images Preview -->
                                <div id="newImagesPreview" class="mt-3 row g-2" style="display: none;"></div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="title" class="form-label">ชื่อเรื่อง <span class="text-danger">*</span></label>
                        <input type="text" class="form-control <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                               id="title" name="title" value="<?php echo e(old('title', $activity->title)); ?>" required
                               placeholder="เช่น ภาพบรรยากาศงานเปิดตัวผลิตภัณฑ์">
                        <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">คำอธิบายสั้น <span class="text-danger">*</span></label>
                        <textarea class="form-control <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                  id="description" name="description" rows="3" required
                                  placeholder="เขียนคำอธิบายสั้นๆ เกี่ยวกับผลงานนี้..."><?php echo e(old('description', $activity->description)); ?></textarea>
                        <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <div class="mb-3">
                        <label for="details" class="form-label">รายละเอียด</label>
                        <textarea class="form-control <?php $__errorArgs = ['details'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                  id="details" name="details" rows="5"
                                  placeholder="เขียนรายละเอียดเพิ่มเติมเกี่ยวกับผลงานนี้..."><?php echo e(old('details', $activity->details)); ?></textarea>
                        <?php $__errorArgs = ['details'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="activity_date" class="form-label">วันที่ <span class="text-danger">*</span></label>
                                <input type="date" class="form-control <?php $__errorArgs = ['activity_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       id="activity_date" name="activity_date" value="<?php echo e(old('activity_date', $activity->activity_date->format('Y-m-d'))); ?>" required>
                                <?php $__errorArgs = ['activity_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="location" class="form-label">สถานที่</label>
                                <input type="text" class="form-control <?php $__errorArgs = ['location'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       id="location" name="location" value="<?php echo e(old('location', $activity->location)); ?>"
                                       placeholder="เช่น วัดพระแก้ว กรุงเทพฯ">
                                <?php $__errorArgs = ['location'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="sort_order" class="form-label">ลำดับการแสดง</label>
                        <input type="number" class="form-control <?php $__errorArgs = ['sort_order'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                               id="sort_order" name="sort_order" value="<?php echo e(old('sort_order', $activity->sort_order)); ?>" min="0"
                               placeholder="0 = แสดงก่อน, ตัวเลขมาก = แสดงหลัง">
                        <?php $__errorArgs = ['sort_order'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        <div class="form-text">ใช้สำหรับจัดเรียงลำดับการแสดงในแกลลอรี่</div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active"
                                   value="1" <?php echo e(old('is_active', $activity->is_active) ? 'checked' : ''); ?>>
                            <label class="form-check-label" for="is_active">
                                <i class="fas fa-eye me-1"></i>เผยแพร่รูปภาพนี้ในแกลลอรี่
                            </label>
                        </div>
                        <div class="form-text">หากไม่เลือก รูปภาพจะถูกบันทึกแต่ไม่แสดงในหน้าเว็บไซต์</div>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>คำแนะนำ:</strong>
                        <ul class="mb-0 mt-2">
                            <li><strong>เปลี่ยนรูปปก:</strong> ใช้สำหรับเปลี่ยนรูปหลักที่แสดงในหน้าแรกและรายการผลงาน</li>
                            <li><strong>เพิ่มรูปในแกลเลอรี่:</strong> ใช้สำหรับเพิ่มรูปเข้าไปในแกลเลอรี่ย่อยของผลงานนี้</li>
                            <li>สามารถทำทั้งสองอย่างพร้อมกันได้ในการบันทึกครั้งเดียว</li>
                        </ul>
                    </div>

                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>บันทึกการแก้ไข
                        </button>
                        <a href="<?php echo e(route('admin.activities')); ?><?php echo e(isset($page) && $page > 1 ? '?page=' . $page : ''); ?>" class="btn btn-secondary">ยกเลิก</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle text-info me-2"></i>ข้อมูลรูปภาพ
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>อัพโหลดเมื่อ:</strong></td>
                        <td><?php echo e($activity->created_at->format('d/m/Y H:i')); ?></td>
                    </tr>
                    <tr>
                        <td><strong>แก้ไขล่าสุด:</strong></td>
                        <td><?php echo e($activity->updated_at->format('d/m/Y H:i')); ?></td>
                    </tr>
                    <tr>
                        <td><strong>ลำดับการแสดง:</strong></td>
                        <td><span class="badge bg-info"><?php echo e($activity->sort_order ?? 0); ?></span></td>
                    </tr>
                    <tr>
                        <td><strong>สถานะ:</strong></td>
                        <td>
                            <?php if($activity->is_active): ?>
                            <span class="badge bg-success">
                                <i class="fas fa-eye me-1"></i>เผยแพร่
                            </span>
                            <?php else: ?>
                            <span class="badge bg-secondary">
                                <i class="fas fa-eye-slash me-1"></i>ซ่อน
                            </span>
                            <?php endif; ?>
                        </td>
                    </tr>
                </table>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tools text-warning me-2"></i>การดำเนินการ
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="<?php echo e(route('activities')); ?>" class="btn btn-outline-primary" target="_blank">
                        <i class="fas fa-external-link-alt me-2"></i>ดูในเว็บไซต์
                    </a>
                    <form action="<?php echo e(route('admin.activities.delete', $activity->id)); ?>" method="POST"
                          id="deleteActivityForm<?php echo e($activity->id); ?>">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="button" class="btn btn-outline-danger w-100"
                                onclick="handleDeleteActivity(<?php echo e($activity->id); ?>)">
                            <i class="fas fa-trash me-2"></i>ลบผลงาน
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageModalLabel">รูปภาพ</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" alt="" class="img-fluid rounded mb-3" style="max-height: 500px;">
                <h6 id="modalTitle" class="mb-2"></h6>
                <p id="modalDescription" class="text-muted"></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ปิด</button>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const coverImageInput = document.getElementById('cover_image');
    const coverImagePreview = document.getElementById('coverImagePreview');
    const coverPreviewImg = document.getElementById('coverPreviewImg');

    const newImagesInput = document.getElementById('new_images');
    const newImagesPreview = document.getElementById('newImagesPreview');

    // Cover image preview functionality
    coverImageInput.addEventListener('change', function(e) {
        const file = e.target.files[0];

        if (file) {
            // Check file size (2MB = 2 * 1024 * 1024 bytes)
            if (file.size > 2 * 1024 * 1024) {
                alert('ขนาดไฟล์ใหญ่เกินไป กรุณาเลือกไฟล์ที่มีขนาดไม่เกิน 2MB');
                coverImageInput.value = '';
                coverImagePreview.style.display = 'none';
                return;
            }

            // Check file type
            const allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
            if (!allowedTypes.includes(file.type)) {
                alert('ประเภทไฟล์ไม่ถูกต้อง กรุณาเลือกไฟล์ JPG, PNG หรือ GIF');
                coverImageInput.value = '';
                coverImagePreview.style.display = 'none';
                return;
            }

            // Show preview
            const reader = new FileReader();
            reader.onload = function(e) {
                coverPreviewImg.src = e.target.result;
                coverImagePreview.style.display = 'block';
            };
            reader.readAsDataURL(file);
        } else {
            coverImagePreview.style.display = 'none';
        }
    });

    // New images preview functionality
    newImagesInput.addEventListener('change', function(e) {
        const files = Array.from(e.target.files);
        newImagesPreview.innerHTML = '';

        if (files.length === 0) {
            newImagesPreview.style.display = 'none';
            return;
        }

        newImagesPreview.style.display = 'block';

        files.forEach((file, index) => {
            // Check file size (2MB = 2 * 1024 * 1024 bytes)
            if (file.size > 2 * 1024 * 1024) {
                alert(`ไฟล์ "${file.name}" มีขนาดใหญ่เกินไป กรุณาเลือกไฟล์ที่มีขนาดไม่เกิน 2MB`);
                return;
            }

            // Check file type
            const allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
            if (!allowedTypes.includes(file.type)) {
                alert(`ไฟล์ "${file.name}" ประเภทไม่ถูกต้อง กรุณาเลือกไฟล์ JPG, PNG หรือ GIF`);
                return;
            }

            // Create preview element
            const colDiv = document.createElement('div');
            colDiv.className = 'col-md-3 col-4';

            const cardDiv = document.createElement('div');
            cardDiv.className = 'card';

            const img = document.createElement('img');
            img.className = 'card-img-top';
            img.style.height = '120px';
            img.style.objectFit = 'cover';

            const cardBody = document.createElement('div');
            cardBody.className = 'card-body p-2';

            const fileName = document.createElement('small');
            fileName.className = 'text-muted d-block';
            fileName.textContent = file.name;

            const fileSize = document.createElement('small');
            fileSize.className = 'text-muted';
            fileSize.textContent = `${(file.size / 1024 / 1024).toFixed(2)} MB`;

            const badge = document.createElement('div');
            badge.className = 'mt-1';
            badge.innerHTML = '<span class="badge bg-success">เพิ่มในแกลเลอรี่</span>';

            // Show preview
            const reader = new FileReader();
            reader.onload = function(e) {
                img.src = e.target.result;
            };
            reader.readAsDataURL(file);

            cardBody.appendChild(fileName);
            cardBody.appendChild(fileSize);
            cardBody.appendChild(badge);
            cardDiv.appendChild(img);
            cardDiv.appendChild(cardBody);
            colDiv.appendChild(cardDiv);
            newImagesPreview.appendChild(colDiv);
        });
    });

    // Image modal functionality
    const imageModal = document.getElementById('imageModal');
    const modalImage = document.getElementById('modalImage');
    const modalTitle = document.getElementById('modalTitle');
    const modalDescription = document.getElementById('modalDescription');

    // Handle image clicks
    document.addEventListener('click', function(e) {
        if (e.target.hasAttribute('data-bs-toggle') && e.target.getAttribute('data-bs-target') === '#imageModal') {
            const imageSrc = e.target.getAttribute('data-image');
            const title = e.target.getAttribute('data-title');
            const description = e.target.getAttribute('data-description');

            modalImage.src = imageSrc;
            modalImage.alt = title;
            modalTitle.textContent = title;
            modalDescription.textContent = description;
        }
    });
});

// Simple view image function
function viewImage(src, title, description = '') {
    console.log('viewImage called:', src, title);

    // Get modal elements
    const modalElement = document.getElementById('imageModal');
    const modalImage = document.getElementById('modalImage');
    const modalTitle = document.getElementById('modalTitle');
    const modalDescription = document.getElementById('modalDescription');

    if (!modalElement || !modalImage) {
        console.error('Modal elements not found');
        return;
    }

    // Set image and text
    modalImage.src = src;
    modalImage.alt = title || '';
    if (modalTitle) modalTitle.textContent = title || '';
    if (modalDescription) modalDescription.textContent = description || '';

    // Show modal using Bootstrap
    const modal = new bootstrap.Modal(modalElement);
    modal.show();
}

// Set cover image function with custom modal
async function setCover(imageId) {
    const confirmed = await showConfirmModal(
        'ยืนยันการตั้งรูปปก',
        'ต้องการตั้งรูปนี้เป็นรูปปกหรือไม่?',
        'ตั้งเป็นรูปปก',
        'ยกเลิก'
    );

    if (confirmed) {
        fetch(`/admin/activities/images/${imageId}/set-cover`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                showNotification('เกิดข้อผิดพลาด: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            showNotification('เกิดข้อผิดพลาดในการเชื่อมต่อ', 'danger');
        });
    }
}

// Delete activity function with custom modal
async function handleDeleteActivity(activityId) {
    const confirmed = await confirmDelete(
        'คุณแน่ใจหรือไม่ที่จะลบผลงานนี้? การกระทำนี้ไม่สามารถยกเลิกได้',
        'ยืนยันการลบผลงาน'
    );

    if (confirmed) {
        document.getElementById(`deleteActivityForm${activityId}`).submit();
    }
}

// Delete image function with custom modal
async function deleteImage(imageId) {
    const confirmed = await confirmDelete(
        'ต้องการลบรูปนี้หรือไม่? การกระทำนี้ไม่สามารถยกเลิกได้',
        'ยืนยันการลบรูปภาพ'
    );

    if (confirmed) {
        fetch(`/admin/activities/images/${imageId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById(`image-${imageId}`).remove();
                showNotification('ลบรูปภาพเรียบร้อยแล้ว', 'success');
            } else {
                showNotification('เกิดข้อผิดพลาด: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            showNotification('เกิดข้อผิดพลาดในการเชื่อมต่อ', 'danger');
        });
    }
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\XAMPP\htdocs\PhuyaiPrajakserviceshop\resources\views/admin/activities/edit.blade.php ENDPATH**/ ?>